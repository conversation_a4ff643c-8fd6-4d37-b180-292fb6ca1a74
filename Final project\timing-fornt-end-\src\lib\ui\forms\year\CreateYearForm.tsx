"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { createYear } from "@/lib/server/actions/year/yearActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createYearSchema = z.object({
  name: z.string().min(1, "Year name is required"),
  department_id: z.string().min(1, "Department is required"),
});

type CreateYearFormData = z.infer<typeof createYearSchema>;

export default function CreateYearForm({ onSuccess }: { onSuccess?: () => void }) {
  const [departments, setDepartments] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateYearFormData>({
    resolver: zodResolver(createYearSchema),
  });

  useEffect(() => {
    getAllDepartments().then((data) => setDepartments(data.departments));
  }, []);

  const onSubmit = async (data: CreateYearFormData) => {
    setError(null);
    setSuccess(false);
    try {
      const response = await createYear({
        name: data.name,
        department_id: +data.department_id,
      });
      console.log('Create year response:', response);
      if (response && (response as any).message) {
        setError((response as any).message);
        return;
      }
      setSuccess(true);
      reset();
      onSuccess?.();
    } catch (e: any) {
      console.error('Create year error:', e);
      setError(e.message || "Failed to create year");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Year created successfully!</span>
        </div>
      )}
      <label>
        Year Name
        <input type="text" {...register("name")} className="input" />
        {errors.name && <span className="text-red-500">{errors.name.message}</span>}
      </label>
      <label>
        Department
        <select {...register("department_id")} className="input">
          <option value="">Select Department</option>
          {departments.map((dep) => (
            <option key={dep.id} value={dep.id}>{dep.name}</option>
          ))}
        </select>
        {errors.department_id && <span className="text-red-500">{errors.department_id.message}</span>}
      </label>
      <Button type="submit" mode="filled" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Year"}
      </Button>
    </form>
  );
}
