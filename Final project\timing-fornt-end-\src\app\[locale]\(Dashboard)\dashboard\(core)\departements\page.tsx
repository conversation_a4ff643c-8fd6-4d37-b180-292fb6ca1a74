import Button from "@/lib/ui/components/global/Buttons/Button";
import {
    Dash<PERSON>ontent,
    DashContentAction,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Timer, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateDepartmentDialog from "./CreateDepartmentDialog";

type Department = {
    id: number;
    name: string;
}[];

export default function DepartmentsPage() {
    const departments: Department = [
        {
            id: 1,
            name: "Computer Science"
        },
        {
            id: 2,
            name: "Mathematics"
        }
    ];

    return (
        <DashContent>
            <DashContenTitle>Departments</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem title="Total Departments" value={departments.length.toString()} icon={<UserPen size={80} />} />
            </DashContentStat>
            <DashContentAction>
                <CreateDepartmentDialog />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Department Name', 'Settings']} />
                <tbody>
                    {departments.map((department) => (
                        <TableTr key={department.id}>
                            <TableTdMain value={department.name} />
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/departments/${department.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <Link href={`/dashboard/departments/${department.id}`}>
                                        <Trash className="text-error dark:text-dark-error" size={16} />
                                    </Link>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </DashContent>
    );
}
