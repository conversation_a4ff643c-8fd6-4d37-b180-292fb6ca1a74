"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateGroupForm from "@/lib/ui/forms/group/CreateGroupForm";
import { Plus } from "lucide-react";

export default function CreateGroupDialog() {
    const [open, setOpen] = useState(false);
    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Group
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Group">
                <CreateGroupForm onSuccess={() => setOpen(false)} />
            </Dialog>
        </>
    );
} 